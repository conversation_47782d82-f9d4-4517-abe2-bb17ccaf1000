# API para gestionar los pasajeros y clientes

El API Manager People es la API que se encarga de gestionar información de pasajeros y clientes. Obtiene los datos a través de la API Manager Connection People y Smart, centralizando el acceso a la información de personas en el ecosistema.

## Frameworks y Herramientas

Para la construcción de dicha API se utilizaron las siguientes tecnologías:

* **[Node.js](https://nodejs.org/docs/latest/api/)**: Node es un runtime de ejecución de JavaScript de código abierto que permite que se pueda ejecutar código JavaScript del lado del servidor.
* **[Express.js](https://expressjs.com/es/4x/api.html)**: Es un framework para desarrollar servidores HTTP bajo la arquitectura REST.
* **[Axios](https://axios-http.com/docs/intro)**: Cliente HTTP basado en promesas para el navegador y Node.js que facilita la comunicación con otras APIs.
* **[Jest](https://jestjs.io/docs/getting-started)**: Jest es un framework de pruebas para JavaScript desarrollado por Facebook. Es ampliamente utilizado en la comunidad de desarrolladores debido a su simplicidad y a las características avanzadas que ofrece.

## Correr localmente

A continuación se describen los pasos para ejecutar el proyecto localmente.

1. Configurar las environment Variables: Agregar las siguientes variables de entorno a un archivo .env. Para obtener el valor de las variables puedes buscar en el repo de DevOps de environment para el proyecto en cuestión [aquí](https://bitbucket.org/cocha-digital/gcp-cocha-development-variables/src/master/). NODE_ENV, API_CONNECTION_PEOPLE, BL_REST_SMART, API_ADAPTER_ALERT, PORT

2. Instalación de dependencias: La API desarrollada es un proyecto Node por lo que hay que instalar las dependencias que se encuentran en el archivo package.json. A continuación se muestra el comando.

   ```
   C:\Users\<USER>\workspace\api-manager-people> npm install
   ```

3. Ejecutar el proyecto: Luego de instaladas las dependencias se puede ejecutar el proyecto. Si todo funciona correctamente el servidor correrá en el puerto 3000. A continuación se muestra el comando de ejecución:
   ```
   C:\Users\<USER>\workspace\api-manager-people> npm start
   ```

## Uso

Para acceder y utilizar esta pieza correctamente, es necesario proporcionar dos parámetros clave:
- FOID: Identificador único de la persona.
- FOIDType: Tipo de identificador que define la persona.
Estos valores permiten realizar un filtrado preciso de la información, asegurando que los datos recuperados sean relevantes y específicos para la consulta. Sin ellos, la pieza no podrá proporcionar los resultados esperados dentro del sistema.

## Mantenedores

* Célula: CARRO
* Encargado: Mauricio Arce email: <EMAIL>