const axios = require('../utils/axios-factory').getInstance();
const config = require('../service/configs-service').getConfig();
const { InternalServerError } = require('../errors');
const utils = require('../utils/utils');
const Result = require('../utils/result');

async function getPeopleByFoid(foid, foidType) {
  const headers = utils.getHeaders();
  const url = config.url.api.people;

  try {
    const { data: result } = await axios.get(url.replace(':foid', foid).replace(':foidType', foidType),{headers,});
    
    return Result.Success(result);
  } catch (err) {
    const status = utils.validateAndReturnStatusCodeError(err);
    const message = utils.validateAndReturnErrorMessage(err);
    
    return Result.Fail(new InternalServerError(status, message, 'ApiPeopleError', []));
  }
}

async function getSearchCustomerByFoid(foid, div) {
  const headers = utils.getHeaders();
  const url = config.url.api.smart.searchCustomer;

  let body = {
    mnemo: '',
    nombre: '',
    paterno: '',
    rut: foid,
    uid: div || '',
  };

  try {
    const { data: resp } = await axios.post(url, body, { headers });
    const result = resp.customer[0];

    return Result.Success(result);

  } catch (err) {
    const status = utils.validateAndReturnStatusCodeError(err);
    const message = utils.validateAndReturnErrorMessage(err);
    
    return Result.Fail(new InternalServerError(status, message, 'ApiSmartError', []));
  }
}

module.exports = {
  getPeopleByFoid,
  getSearchCustomerByFoid,
};