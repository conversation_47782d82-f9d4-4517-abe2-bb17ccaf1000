const express = require('express');
const { StatusCodes } = require('http-status-codes');
const { InfluxDbError } = require('../errors');

// Controllers
const healthController = require('../controllers/health-controller');
const peopleController = require('../controllers/people-controller');
const grafanaLogger = require('../service/alert-cloud-function-service')



const router = express.Router();

router.get('/health', async (_req, res) =>{
  const result = await healthController.health();

  if (result.success) {
    const { data } = result;
    res.status(StatusCodes.OK).json(data);
  } else {
    const err = result.error;
    const status = err.status || StatusCodes.INTERNAL_SERVER_ERROR;
    const error = {
      name: err.name,
      message: err.message || 'An unknown error happened',
      errors: err.errors || [],
    };
    res.status(status).json(error);
  }
});

router.get('/people/:foid/:foidType', async (req, res) => {
  const { foid, foidType } = req.params;
  const result = await peopleController.getByFoid(foid, foidType);

  if (result.success) {
    const { data } = result;
    res.status(StatusCodes.OK).json(data);
  } else {
    const err = result.error;
    const status = err.status || StatusCodes.INTERNAL_SERVER_ERROR;
    const error = {
      name: err.name || 'Error',
      message: err.message || 'An unknown error happened',
      errors: err.errors || [],
    };
    grafanaLogger.sendAlert(new InfluxDbError(status, false, error.message, null, 'Foid Error'));
    res.status(status).json(error);
  }
});

router.get('/:foid/:foidType', async (req, res) => {
  const { foid, foidType } = req.params;
  const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

  if (result.success) {
    const { data } = result;
    res.status(StatusCodes.OK).json(data);
  } else {
    const err = result.error;
    const status = err.status || StatusCodes.INTERNAL_SERVER_ERROR;
    const error = {
      name: err.name || 'Error',
      message: err.message || 'An unknown error happened',
      errors: err.errors || [],
    };
    grafanaLogger.sendAlert(new InfluxDbError(status, false, error.message, null, 'Access to Api failed'));
    res.status(status).json(error);
  }
});

module.exports = router;
