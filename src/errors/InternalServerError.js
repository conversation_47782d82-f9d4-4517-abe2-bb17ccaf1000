const { Error: CommonError } = require('common-errors');

class InternalServerError extends CommonError {
  /**
   * @param message        message
   * @param inner_error the Error instance that caused the current error.
   *                    Stack trace will be appended.
   */
  constructor(status,message,name = `Error`,errors = [], innerError = {}) {
    super('', innerError);
    this.status = status;
    this.name = name;
    this.message = message;
    this.errors = errors;
  }
}

module.exports = InternalServerError;
