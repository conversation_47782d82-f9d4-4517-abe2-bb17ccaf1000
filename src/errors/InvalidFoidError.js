const { ArgumentError } = require('common-errors');

class InvalidFoidError extends ArgumentError {
  /**
   * @param foid        foid
   * @param inner_error the Error instance that caused the current error.
   *                    Stack trace will be appended.
   */
  constructor(foid, innerError = {}) {
    super('', innerError);
    this.name = 'InvalidFoidError';
    this.message = `'${foid}' is not valid`;
  }
}

module.exports = InvalidFoidError;
