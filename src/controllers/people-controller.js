const peopleFacade = require('../facade/facade');
const { ArgumentError } = require('common-errors');
const { InvalidFoidError } = require('../errors');
const Result = require('../utils/result');
const utils = require('../utils/utils');

async function getByFoid( foid, foidType ) {
  if (!foidType) {
    return Result.Fail(new ArgumentError(`${foidType} should be RUT or PASSPORT`));
  }
  if (!foid) {
    return Result.Fail(new ArgumentError(`${foid} can not be null or undefined`));
  }
  const result = await peopleFacade.getPeopleByFoid(foid, foidType);
  return Result.Success(result.data);
}

async function getSearchCustomerByFoid( foid, foidType ) {
  if (!foid || foid === ':foid') {
    return Result.Fail(new InvalidFoidError(foid));
  }
  if (isNaN(parseInt(foid)) && foidType === 'RUT') {
    return Result.Fail(new InvalidFoidError(foid));
  }
  if (!foidType || foidType === ':foidType' || !utils.isValidFoidType(foidType)) {
    return Result.Fail(new InvalidFoidError(`${foidType}`));
  }

  const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

  if (!result.success){
    return Result.Fail(result.error)
  }

  return Result.Success({ people: result.data });
}

module.exports = {
  getByFoid,
  getSearchCustomerByFoid,
};
