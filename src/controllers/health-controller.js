const os = require('os');
const pkg = require('../../package.json');


const Result = require('../utils/result');
const { StatusCodes } = require('http-status-codes');
const { InternalServerError } = require('../errors');


async function health() {
try{
  const health = {
    hostname: os.hostname(),
    app: {
      version: pkg.version,
      build: process.env.APP_BUILD || 'no-build',
      commit: process.env.APP_COMMIT || 'no-commit',
      name: pkg.name,
      environment: process.env.NODE_ENV || 'local',
    },
  };

  return Result.Success(health);

} catch(err) {
  const name = (err.constructor?.name) || 'Error';
  const status = err.status || StatusCodes.INTERNAL_SERVER_ERROR;
  const message = err.message || 'Unknow error';

  return Result.Fail(new InternalServerError(status, message, name));
}
}



module.exports = {
  health,
};
