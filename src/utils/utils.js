const context = require('../service/context-service');

function getHeaders() {
  const trackId = context.get('TrackId') || '';
  const flowId = context.get('FlowId') || '';
  return {
    TrackId: trackId,
    FlowId: flowId,
    'Cache-Control': 'no-cache',
  };
}

function validateAndReturnStatusCodeError(error) {
    let statusCode;
    if (error.status) {
      statusCode = error.status;
    } else if (error.response?.status) {
      statusCode = error.response.status;
    } else if (error.request?.status) {
      statusCode = error.request.status;
    } else {
      statusCode = 500;
    }
    return statusCode;
  }

function validateAndReturnErrorMessage(error) {
  let errorMessage = error.message ? error.message : "UnknowError";
  return errorMessage;
}

function isValidFoidType(foidType) {
  return foidType.trim().toUpperCase() === 'RUT' || foidType.trim().toUpperCase() === 'PASSPORT';
}


module.exports = {
  getHeaders,
  validateAndReturnStatusCodeError,
  validateAndReturnErrorMessage,
  isValidFoidType,
};