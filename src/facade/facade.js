const peopleService= require('../service/people-service');
const Result = require('../utils/result');

async function getPeopleByFoid(foid, foidType) {
  const result = await peopleService.getPeopleByFoid(foid, foidType);

  return result;
};

async function getSearchCustomerByFoid(foid, foidType) {
  let div = '';

  if (foidType.toUpperCase() === 'RUT') {
    foid = foid.replace(/\./g, '');
    if (foid.includes('-')) {
      [foid, div] = foid.split('-');
    } else {
      div = foid.slice(-1);
      foid = foid.slice(0, -1);
    }
  }

  const result = await peopleService.getSearchCustomerByFoid(foid, div);
  if (!result.success) {
    return Result.Fail(result.error);
  }

  let profile = result.data;

  if(profile){
    const phoneSplit = profile.fonoCelular ? profile.fonoCelular.split('-') : [];
    const [phoneCountry = '', phoneArea = '', phoneNumber = ''] = phoneSplit;
  
    const formattedPhoneNumber = phoneSplit.length >= 3 && phoneSplit[2] ? phoneSplit[2] : phoneNumber;
    profile = {
      id: profile.kn,
      foid: div ? `${foid}-${div}` : foid,
      foidType: foidType,
      foidExpiration: profile.foidExpiration,
      foidIssued: profile.foidIssued,
      foidCountry: '', 
      name: profile.nombres,
      firstLastName: profile.paterno,
      secondLastName: profile.materno,
      gender: profile.sexo,
      email: profile.email,
      birthDate: profile.fecNac,
      age: '',  
      type: '',  
      phoneCountry,
      phoneArea,
      phoneNumber: formattedPhoneNumber,
    };
  }

  return Result.Success(profile || null);
}

module.exports = {
  getPeopleByFoid,
  getSearchCustomerByFoid,
};
