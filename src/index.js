const logger = require('./utils/logger-factory').getLogger();
const dotenv = require('dotenv');
const express = require('express');
const bodyParser = require('body-parser');
const router = require('./config/routes');
const cors = require('cors');
const swaggerUi = require('swagger-ui-express');
const swaggerSpec = require('../docs/swagger');

if (process.env.NODE_ENV !== 'production') {
  dotenv.config();
}

const corsOptions = {
  origin: [/cocha\.com/],
  methods: ['GET', 'POST', 'PUT', 'DELETE'],
  allowedHeaders: ['TrackId', 'FlowId', 'Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'PaymentIntention'],
};

const environment = process.env.NODE_ENV || 'local';
const port = process.env.PORT || 8004;
const server = express({});
server.disable('x-powered-by');

server.use(cors(corsOptions));
server.use(bodyParser.json());
server.use(bodyParser.urlencoded({ extended: true }));

server.use((req, res, next) => {
  res.set(
    'Access-Control-Allow-Headers',
    'TrackId, FlowId, Origin, X-Requested-With, Content-Type, Accept, Authorization, PaymentIntention'
  );
  res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
  next();
});

if (process.env.NODE_ENV != 'production'){
  server.use('/api-docs', (_req, res) => {
    res.send(swaggerSpec);
  });
  server.use('/docs/', swaggerUi.serve, swaggerUi.setup(swaggerSpec));
}

server.use('/', router);


server.listen({ port }, () => {
  logger.info(`Server started as '${environment}' environment on http://localhost:${port}`);
});
