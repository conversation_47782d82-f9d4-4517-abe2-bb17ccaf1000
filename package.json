{"name": "api-manager-people", "version": "2.2.1", "description": "", "main": "src/index.js", "scripts": {"start": "node src/index.js", "debug": "nodemon --inspect src/index.js", "test": "jest", "lint": "eslint . --fix"}, "author": "<PERSON>", "license": "ISC", "dependencies": {"@cocha/cocha-logger": "3.0.28", "axios": "1.8.1", "axios-mock-adapter": "1.22.0", "common-errors": "1.2.0", "cors": "2.8.5", "dotenv": "16.4.5", "express": "4.21.2", "express-http-context": "1.2.4", "fs": "0.0.1-security", "http-status-codes": "2.3.0", "lodash": "4.17.21", "moment": "2.30.1", "rut.js": "2.1.0", "swagger-jsdoc": "6.2.8", "swagger-ui-express": "5.0.1"}, "devDependencies": {"ava": "6.1.2", "jest": "29.7.0", "nodemon": "3.1.0", "nyc": "15.1.0", "prettier": "3.2.5", "supertest": "^7.1.0"}, "nyc": {"all": true, "include": ["src/*/*.js"], "exclude": ["**/index.js", "**/routes.js", "**/config/*", "**/controllers/health-controller.js", "**/errors/index.js", "**/mocks/*", "**/models/*", "**/services/configs-service.js", "**/*.test.js", "**/*.config.js"]}}