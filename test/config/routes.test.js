// Mock dependencies first
jest.mock('../../src/controllers/health-controller');
jest.mock('../../src/controllers/people-controller');
jest.mock('../../src/service/alert-cloud-function-service');

// Then import the modules
const express = require('express');
const request = require('supertest');
const { StatusCodes } = require('http-status-codes');
const Result = require('../../src/utils/result');
const healthController = require('../../src/controllers/health-controller');
const peopleController = require('../../src/controllers/people-controller');
const grafanaLogger = require('../../src/service/alert-cloud-function-service');
const router = require('../../src/config/routes');

describe('Routes', () => {
  let app;

  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Create a new Express app for each test
    app = express();
    app.use('/', router);
  });

  describe('GET /health', () => {
    it('should return 200 and health data when successful', async () => {
      // Arrange
      const mockHealthData = {
        hostname: 'test-hostname',
        app: {
          version: '1.0.0',
          build: 'test-build',
          commit: 'test-commit',
          name: 'api-manager-people',
          environment: 'test-environment'
        }
      };
      healthController.health.mockResolvedValue(Result.Success(mockHealthData));

      // Act & Assert
      const response = await request(app).get('/health');
      expect(response.status).toBe(StatusCodes.OK);
      expect(response.body).toEqual(mockHealthData);
      expect(healthController.health).toHaveBeenCalled();
    });

    it('should return error status and details when health check fails', async () => {
      // Arrange
      const mockError = {
        status: StatusCodes.INTERNAL_SERVER_ERROR,
        name: 'TestError',
        message: 'Test error message',
        errors: ['Error 1', 'Error 2']
      };
      healthController.health.mockResolvedValue(Result.Fail(mockError));

      // Act & Assert
      const response = await request(app).get('/health');
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: mockError.message,
        errors: mockError.errors
      });
      expect(healthController.health).toHaveBeenCalled();
    });

    it('should handle errors without status code', async () => {
      // Arrange
      const mockError = {
        name: 'TestError',
        message: 'Test error message',
        errors: ['Error 1', 'Error 2']
      };
      healthController.health.mockResolvedValue(Result.Fail(mockError));

      // Act & Assert
      const response = await request(app).get('/health');
      expect(response.status).toBe(StatusCodes.INTERNAL_SERVER_ERROR);
      expect(response.body).toEqual({
        name: mockError.name,
        message: mockError.message,
        errors: mockError.errors
      });
      expect(healthController.health).toHaveBeenCalled();
    });

    it('should handle errors without message', async () => {
      // Arrange
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        name: 'TestError',
        errors: ['Error 1', 'Error 2']
      };
      healthController.health.mockResolvedValue(Result.Fail(mockError));

      // Act & Assert
      const response = await request(app).get('/health');
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: 'An unknown error happened',
        errors: mockError.errors
      });
      expect(healthController.health).toHaveBeenCalled();
    });

    it('should handle errors without errors array', async () => {
      // Arrange
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        name: 'TestError',
        message: 'Test error message'
      };
      healthController.health.mockResolvedValue(Result.Fail(mockError));

      // Act & Assert
      const response = await request(app).get('/health');
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: mockError.message,
        errors: []
      });
      expect(healthController.health).toHaveBeenCalled();
    });
  });

  describe('GET /people/:foid/:foidType', () => {
    it('should return 200 and people data when successful', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockPeopleData = {
        id: 1505,
        foid: '22.222.222-2',
        foidType: 'RUT',
        foidCountry: 'CL',
        name: 'Sebastian Ignacio',
        firstLastName: 'Lincoqueo',
        secondLastName: '',
        gender: 'M',
        email: '',
        foidExpiration: null,
        foidIssued: null,
        birthDate: '2000-12-08',
        age: 24,
        type: 'ADT'
      };
      peopleController.getByFoid.mockResolvedValue(Result.Success(mockPeopleData));

      // Act & Assert
      const response = await request(app).get(`/people/${foid}/${foidType}`);
      expect(response.status).toBe(StatusCodes.OK);
      expect(response.body).toEqual(mockPeopleData);
      expect(peopleController.getByFoid).toHaveBeenCalledWith(foid, foidType);
    });

    it('should return error status and details when getByFoid fails', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        name: 'TestError',
        message: 'Test error message',
        errors: ['Error 1', 'Error 2']
      };
      peopleController.getByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/people/${foid}/${foidType}`);
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: mockError.message,
        errors: mockError.errors
      });
      expect(peopleController.getByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });

    it('should handle errors without name and errors in getByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        message: 'Test error message'
      };
      peopleController.getByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/people/${foid}/${foidType}`);
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: 'Error',
        message: mockError.message,
        errors: []
      });
      expect(peopleController.getByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });

    it('should handle errors without message in getByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        name: 'TestError'
      };
      peopleController.getByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/people/${foid}/${foidType}`);
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: 'An unknown error happened',
        errors: []
      });
      expect(peopleController.getByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });

    it('should handle errors without status in getByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        message: 'Test error message'
      };
      peopleController.getByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/people/${foid}/${foidType}`);
      expect(response.status).toBe(StatusCodes.INTERNAL_SERVER_ERROR);
      expect(response.body).toEqual({
        name: 'Error',
        message: mockError.message,
        errors: []
      });
      expect(peopleController.getByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });
  });

  describe('GET /:foid/:foidType', () => {
    it('should return 200 and customer data when successful', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockCustomerData = {
        id: 3659529,
        foid: '22222-2',
        foidType: 'RUT',
        foidCountry: '',
        name: 'GRUPO ARAUCO',
        firstLastName: 'CURITIBA',
        secondLastName: null,
        gender: null,
        email: '<EMAIL>',
        birthDate: null,
        age: '',
        type: '',
        phoneCountry: '',
        phoneArea: '',
        phoneNumber: ''
      };
      peopleController.getSearchCustomerByFoid.mockResolvedValue(Result.Success(mockCustomerData));

      // Act & Assert
      const response = await request(app).get(`/${foid}/${foidType}`);
      expect(response.status).toBe(StatusCodes.OK);
      expect(response.body).toEqual(mockCustomerData);
      expect(peopleController.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
    });

    it('should return error status and details when getSearchCustomerByFoid fails', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        name: 'TestError',
        message: 'Test error message',
        errors: ['Error 1', 'Error 2']
      };
      peopleController.getSearchCustomerByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/${foid}/${foidType}`);
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: mockError.message,
        errors: mockError.errors
      });
      expect(peopleController.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });

    it('should handle errors without name and errors in getSearchCustomerByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        message: 'Test error message'
      };
      peopleController.getSearchCustomerByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/${foid}/${foidType}`);
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: 'Error',
        message: mockError.message,
        errors: []
      });
      expect(peopleController.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });

    it('should handle errors without message in getSearchCustomerByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        status: StatusCodes.BAD_REQUEST,
        name: 'TestError'
      };
      peopleController.getSearchCustomerByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/${foid}/${foidType}`);
      expect(response.status).toBe(mockError.status);
      expect(response.body).toEqual({
        name: mockError.name,
        message: 'An unknown error happened',
        errors: []
      });
      expect(peopleController.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });

    it('should handle errors without status in getSearchCustomerByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = {
        message: 'Test error message'
      };
      peopleController.getSearchCustomerByFoid.mockResolvedValue(Result.Fail(mockError));
      grafanaLogger.sendAlert.mockReturnValue();

      // Act & Assert
      const response = await request(app).get(`/${foid}/${foidType}`);
      expect(response.status).toBe(StatusCodes.INTERNAL_SERVER_ERROR);
      expect(response.body).toEqual({
        name: 'Error',
        message: mockError.message,
        errors: []
      });
      expect(peopleController.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
      expect(grafanaLogger.sendAlert).toHaveBeenCalled();
    });
  });
});
