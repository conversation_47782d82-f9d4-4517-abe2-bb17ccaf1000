// Setup mock implementation
const mockConfig = {
  url: {
    api: {
      people: 'mock-api-url/:foid/:foidType',
      smart: {
        searchCustomer: 'mock-smart-api-url/searchCustomer'
      }
    }
  },
  alert: {
    url: 'mock-alert-url/send'
  }
};

// Mock dependencies first
jest.mock('../../src/config/environment/config', () => mockConfig);

// Then import the modules
const configsService = require('../../src/service/configs-service');
const config = require('../../src/config/environment/config');

describe('Configs Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getConfig', () => {
    it('should return the config object', () => {
      // Act
      const result = configsService.getConfig();

      // Assert
      expect(result).toBe(config);
      expect(result).toEqual(mockConfig);
    });

    it('should return the same config object on multiple calls', () => {
      // Act
      const result1 = configsService.getConfig();
      const result2 = configsService.getConfig();

      // Assert
      expect(result1).toBe(result2);
    });
  });
});
