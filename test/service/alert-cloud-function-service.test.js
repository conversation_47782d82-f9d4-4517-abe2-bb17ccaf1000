const alertService = require('../../src/service/alert-cloud-function-service');
const { InfluxDbError } = require('../../src/errors');

// Use the mock from the new location
jest.mock('@cocha/cocha-logger', () => require('../../__mocks__/logger/cocha-logger'));
const grafanaLogger = require('@cocha/cocha-logger').getLogger();

describe('Alert Cloud Function Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('sendAlert', () => {
    it('should call grafanaLogger.sendGrafanaLogObj with the provided error object', async () => {
      // Arrange
      const mockError = new InfluxDbError(500, true, 'Test error message', 12345, 'Test Error');

      // Act
      await alertService.sendAlert(mockError);

      // Assert
      expect(grafanaLogger.sendGrafanaLogObj).toHaveBeenCalledWith(mockError);
    });

    it('should handle different types of error objects', async () => {
      // Test with different error types
      const testCases = [
        new InfluxDbError(400, true, 'Bad Request', 12345, 'Validation Error'),
        new InfluxDbError(404, true, 'Not Found', 67890, 'Resource Error'),
        new InfluxDbError(500, true, 'Internal Server Error', null, 'Server Error'),
        { custom: 'error object' } // Custom error object
      ];

      for (const testCase of testCases) {
        // Reset mocks for each test case
        jest.clearAllMocks();

        // Act
        await alertService.sendAlert(testCase);

        // Assert
        expect(grafanaLogger.sendGrafanaLogObj).toHaveBeenCalledWith(testCase);
      }
    });

    it('should call grafanaLogger.sendGrafanaLogObj even if it throws an error', async () => {
      // Arrange
      const mockError = new InfluxDbError(500, true, 'Test error message', null, 'Test Error');
      grafanaLogger.sendGrafanaLogObj.mockImplementation(() => {
        throw new Error('Logger error');
      });

      // Act & Assert
      try {
        await alertService.sendAlert(mockError);
      } catch (error) {
        // Expected to throw
      }
      expect(grafanaLogger.sendGrafanaLogObj).toHaveBeenCalledWith(mockError);
    });
  });
});
