// Setup mock config
const mockConfig = {
  url: {
    api: {
      people: 'mock-api-url/:foid/:foidType',
      smart: {
        searchCustomer: 'mock-smart-api-url/searchCustomer'
      }
    }
  }
};

// Mock dependencies first
jest.mock('axios'); // Use the global axios mock from __mocks__/axios.js
jest.mock('../../src/utils/utils');
jest.mock('../../src/utils/axios-factory', () => {
  // Import the global axios mock
  const mockAxios = require('axios');
  return {
    getInstance: jest.fn().mockReturnValue(mockAxios)
  };
});
jest.mock('../../src/service/configs-service', () => ({
  getConfig: jest.fn().mockReturnValue(mockConfig)
}));

// Then import the modules
const peopleService = require('../../src/service/people-service');
const utils = require('../../src/utils/utils');
const { InternalServerError } = require('../../src/errors');
const errorsMock = require('../../__mocks__/errors/errors.json');
const mockAxios = require('axios'); // Import the global axios mock for use in tests

describe('People Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Setup common mocks
    utils.getHeaders.mockReturnValue({
      TrackId: 'test-track-id',
      FlowId: 'test-flow-id',
      'Cache-Control': 'no-cache'
    });
  });

  describe('getPeopleByFoid', () => {
    it('should return a success result when API call is successful', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockData = require('../../__mocks__/people/getByFoid.json').service;
      const mockResponse = {
        data: mockData
      };

      mockAxios.get.mockResolvedValue(mockResponse);

      // Act
      const result = await peopleService.getPeopleByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse.data);
      expect(mockAxios.get).toHaveBeenCalledWith(
        'mock-api-url/12345678/RUT',
        { headers: expect.any(Object) }
      );
      expect(utils.getHeaders).toHaveBeenCalled();
    });

    it('should return a fail result when API call fails', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = new Error('API error');
      mockError.response = { status: 500 };

      mockAxios.get.mockRejectedValue(mockError);
      utils.validateAndReturnStatusCodeError.mockReturnValue(500);
      utils.validateAndReturnErrorMessage.mockReturnValue('API error');

      // Act
      const result = await peopleService.getPeopleByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InternalServerError);
      expect(result.error.status).toBe(500);
      expect(result.error.message).toBe('API error');
      expect(mockAxios.get).toHaveBeenCalledWith(
        'mock-api-url/12345678/RUT',
        { headers: expect.any(Object) }
      );
      expect(utils.getHeaders).toHaveBeenCalled();
      expect(utils.validateAndReturnStatusCodeError).toHaveBeenCalledWith(mockError);
      expect(utils.validateAndReturnErrorMessage).toHaveBeenCalledWith(mockError);
    });
  });

  describe('getSearchCustomerByFoid', () => {
    it('should return a success result when API call is successful', async () => {
      // Arrange
      const foid = '12345678';
      const div = '9';
      const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
      const mockResponse = {
        data: {
          customer: [mockCustomerData]
        }
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      // Act
      const result = await peopleService.getSearchCustomerByFoid(foid, div);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockResponse.data.customer[0]);
      expect(mockAxios.post).toHaveBeenCalledWith(
        'mock-smart-api-url/searchCustomer',
        {
          mnemo: '',
          nombre: '',
          paterno: '',
          rut: foid,
          uid: div
        },
        { headers: expect.any(Object) }
      );
      expect(utils.getHeaders).toHaveBeenCalled();
    });

    it('should handle empty div parameter', async () => {
      // Arrange
      const foid = '12345678';
      const div = '';
      const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
      const mockResponse = {
        data: {
          customer: [mockCustomerData]
        }
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      // Act
      const result = await peopleService.getSearchCustomerByFoid(foid, div);

      // Assert
      expect(result.success).toBe(true);
      expect(mockAxios.post).toHaveBeenCalledWith(
        'mock-smart-api-url/searchCustomer',
        {
          mnemo: '',
          nombre: '',
          paterno: '',
          rut: foid,
          uid: ''
        },
        { headers: expect.any(Object) }
      );
    });

    it('should handle null div parameter', async () => {
      // Arrange
      const foid = '12345678';
      const div = null;
      const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
      const mockResponse = {
        data: {
          customer: [mockCustomerData]
        }
      };

      mockAxios.post.mockResolvedValue(mockResponse);

      // Act
      const result = await peopleService.getSearchCustomerByFoid(foid, div);

      // Assert
      expect(result.success).toBe(true);
      expect(mockAxios.post).toHaveBeenCalledWith(
        'mock-smart-api-url/searchCustomer',
        {
          mnemo: '',
          nombre: '',
          paterno: '',
          rut: foid,
          uid: ''
        },
        { headers: expect.any(Object) }
      );
    });

    it('should return a fail result when API call fails', async () => {
      // Arrange
      const foid = '12345678';
      const div = '9';
      const mockError = new Error(errorsMock.apiError.message);
      mockError.response = { status: errorsMock.apiError.status };

      mockAxios.post.mockRejectedValue(mockError);
      utils.validateAndReturnStatusCodeError.mockReturnValue(errorsMock.apiError.status);
      utils.validateAndReturnErrorMessage.mockReturnValue(errorsMock.apiError.message);

      // Act
      const result = await peopleService.getSearchCustomerByFoid(foid, div);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InternalServerError);
      expect(result.error.status).toBe(errorsMock.apiError.status);
      expect(result.error.message).toBe(errorsMock.apiError.message);
      expect(mockAxios.post).toHaveBeenCalledWith(
        'mock-smart-api-url/searchCustomer',
        expect.any(Object),
        { headers: expect.any(Object) }
      );
      expect(utils.getHeaders).toHaveBeenCalled();
      expect(utils.validateAndReturnStatusCodeError).toHaveBeenCalledWith(mockError);
      expect(utils.validateAndReturnErrorMessage).toHaveBeenCalledWith(mockError);
    });
  });
});
