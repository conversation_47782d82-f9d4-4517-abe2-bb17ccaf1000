// Mock dependencies first
jest.mock('express-http-context');

// Then import the modules
const contextService = require('../../src/service/context-service');
const httpContext = require('express-http-context');

describe('Context Service', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('get', () => {
    it('should call httpContext.get with the provided key', () => {
      // Arrange
      const key = 'testKey';
      const expectedValue = 'testValue';
      httpContext.get.mockReturnValue(expectedValue);

      // Act
      const result = contextService.get(key);

      // Assert
      expect(result).toBe(expectedValue);
      expect(httpContext.get).toHaveBeenCalledWith(key);
    });

    it('should return undefined when the key does not exist', () => {
      // Arrange
      const key = 'nonExistentKey';
      httpContext.get.mockReturnValue(undefined);

      // Act
      const result = contextService.get(key);

      // Assert
      expect(result).toBeUndefined();
      expect(httpContext.get).toHaveBeenCalledWith(key);
    });
  });

  describe('set', () => {
    it('should call httpContext.set with the provided key and value', () => {
      // Arrange
      const key = 'testKey';
      const value = 'testValue';

      // Act
      contextService.set(key, value);

      // Assert
      expect(httpContext.set).toHaveBeenCalledWith(key, value);
    });

    it('should handle different types of values', () => {
      // Test with different value types
      const testCases = [
        { key: 'stringKey', value: 'string value' },
        { key: 'numberKey', value: 123 },
        { key: 'booleanKey', value: true },
        { key: 'objectKey', value: { prop: 'value' } },
        { key: 'arrayKey', value: [1, 2, 3] },
        { key: 'nullKey', value: null },
        { key: 'undefinedKey', value: undefined }
      ];

      for (const testCase of testCases) {
        // Reset mocks for each test case
        jest.clearAllMocks();

        // Act
        contextService.set(testCase.key, testCase.value);

        // Assert
        expect(httpContext.set).toHaveBeenCalledWith(testCase.key, testCase.value);
      }
    });
  });
});
