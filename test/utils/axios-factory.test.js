// Mock dependencies first
jest.mock('axios');
jest.mock('@cocha/cocha-logger', () => require('../../__mocks__/logger/cocha-logger'));

// Then import the modules
const axios = require('axios');
const { axiosWithLogger } = require('../../__mocks__/logger/cocha-logger');

// Create a mock logger
const mockLogger = { info: jest.fn(), error: jest.fn() };

// Mock logger-factory after creating the mock logger
jest.mock('../../src/utils/logger-factory', () => ({
  getLogger: jest.fn().mockReturnValue(mockLogger)
}));

// Import the module after setting up all mocks
const axiosFactory = require('../../src/utils/axios-factory');

describe('Axios Factory', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  it('should return an axios instance with logger', () => {
    // Arrange
    const mockAxiosInstance = { get: jest.fn(), post: jest.fn() };
    axiosWithLogger.getInstance.mockReturnValue(mockAxiosInstance);

    // Act
    const result = axiosFactory.getInstance();

    // Assert
    expect(result).toBe(mockAxiosInstance);
    expect(axiosWithLogger.getInstance).toHaveBeenCalledWith(mockLogger, axios);
  });
});
