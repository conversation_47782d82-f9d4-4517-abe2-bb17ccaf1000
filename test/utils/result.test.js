const Result = require('../../src/utils/result');

describe('Result', () => {
  describe('constructor', () => {
    it('should create a result with the provided values', () => {
      // Arrange & Act
      const success = true;
      const data = { id: 1, name: 'Test' };
      const error = null;
      const result = new Result(success, data, error);

      // Assert
      expect(result.success).toBe(success);
      expect(result.data).toBe(data);
      expect(result.error).toBe(error);
    });
  });

  describe('Success', () => {
    it('should create a success result with the provided data', () => {
      // Arrange
      const data = { id: 1, name: 'Test' };

      // Act
      const result = Result.Success(data);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBe(data);
      expect(result.error).toBeNull();
    });

    it('should create a success result with null data', () => {
      // Act
      const result = Result.Success(null);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
      expect(result.error).toBeNull();
    });
  });

  describe('Fail', () => {
    it('should create a fail result with the provided error', () => {
      // Arrange
      const error = new Error('Test error');

      // Act
      const result = Result.Fail(error);

      // Assert
      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBe(error);
    });

    it('should create a fail result with null error', () => {
      // Act
      const result = Result.Fail(null);

      // Assert
      expect(result.success).toBe(false);
      expect(result.data).toBeNull();
      expect(result.error).toBeNull();
    });
  });
});
