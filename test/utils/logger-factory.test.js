// Mock dependencies first
jest.mock('@cocha/cocha-logger', () => require('../../__mocks__/logger/cocha-logger'));
jest.mock('../../package.json', () => ({ name: 'api-manager-people' }));

describe('Logger Factory', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();

    // Reset modules to clear any cached instances
    jest.resetModules();
  });

  it('should create a new logger instance when called for the first time', () => {
    // Arrange
    const logger = require('../../__mocks__/logger/cocha-logger');
    const mockLoggerInstance = { info: jest.fn(), error: jest.fn() };
    logger.getLogger.mockReturnValue(mockLoggerInstance);

    // Import the module after setting up the mock
    const loggerFactory = require('../../src/utils/logger-factory');

    // Act
    const result = loggerFactory.getLogger();

    // Assert
    expect(result).toBe(mockLoggerInstance);
    expect(logger.getLogger).toHaveBeenCalledWith({ appName: 'api-manager-people' });
    expect(logger.getLogger).toHaveBeenCalledTimes(1);
  });

  it('should return the existing logger instance when called multiple times', () => {
    // Arrange
    const logger = require('../../__mocks__/logger/cocha-logger');
    const mockLoggerInstance = { info: jest.fn(), error: jest.fn() };
    logger.getLogger.mockReturnValue(mockLoggerInstance);

    // Import the module after setting up the mock
    const loggerFactory = require('../../src/utils/logger-factory');

    // Act
    const result1 = loggerFactory.getLogger();
    const result2 = loggerFactory.getLogger();
    const result3 = loggerFactory.getLogger();

    // Assert
    expect(result1).toBe(result2);
    expect(result2).toBe(result3);
    expect(logger.getLogger).toHaveBeenCalledTimes(1);
  });
});
