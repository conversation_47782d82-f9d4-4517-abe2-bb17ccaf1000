// Mock dependencies first
jest.mock('axios'); // Use the global axios mock from __mocks__/axios.js
jest.mock('@cocha/cocha-logger', () => require('../../__mocks__/logger/cocha-logger'));
jest.mock('../../src/utils/axios-factory', () => {
  // Import the global axios mock
  const mockAxios = require('axios');
  return {
    getInstance: jest.fn().mockReturnValue(mockAxios)
  };
});
jest.mock('../../src/service/configs-service');
jest.mock('../../src/service/people-service');

// Then import the modules
const peopleFacade = require('../../src/facade/facade');
const peopleService = require('../../src/service/people-service');
const Result = require('../../src/utils/result');
const errorsMock = require('../../__mocks__/errors/errors.json');

describe('People Facade', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getPeopleByFoid', () => {
    it('should return the result from peopleService.getPeopleByFoid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockData = require('../../__mocks__/people/getByFoid.json').service;
      const mockResult = Result.Success(mockData);

      peopleService.getPeopleByFoid.mockResolvedValue(mockResult);

      // Act
      const result = await peopleFacade.getPeopleByFoid(foid, foidType);

      // Assert
      expect(result).toBe(mockResult);
      expect(peopleService.getPeopleByFoid).toHaveBeenCalledWith(foid, foidType);
    });
  });

  describe('getSearchCustomerByFoid', () => {
    it('should format RUT foid with dash and call peopleService.getSearchCustomerByFoid', async () => {
      // Arrange
      const foid = '12345678-9';
      const foidType = 'RUT';
      const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
      const mockServiceResult = Result.Success(mockCustomerData);

      peopleService.getSearchCustomerByFoid.mockResolvedValue(mockServiceResult);

      // Act
      const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);

      // Create expected data based on the mock
      const expectedData = {
        id: mockCustomerData.kn,
        foid: '12345678-9',
        foidType: 'RUT',
        foidExpiration: undefined,
        foidIssued: undefined,
        foidCountry: '',
        name: mockCustomerData.nombres,
        firstLastName: mockCustomerData.paterno,
        secondLastName: mockCustomerData.materno || '',
        gender: mockCustomerData.sexo || '',
        email: mockCustomerData.email,
        birthDate: mockCustomerData.fecNac || '',
        age: '',
        type: '',
        phoneCountry: '',
        phoneArea: '',
        phoneNumber: ''
      };

      // If the mock has a phone number, parse it
      if (mockCustomerData.fonoCelular === '56-9-12345678') {
        expectedData.phoneCountry = '56';
        expectedData.phoneArea = '9';
        expectedData.phoneNumber = '12345678';
      }

      expect(result.data).toEqual(expectedData);
      expect(peopleService.getSearchCustomerByFoid).toHaveBeenCalledWith('12345678', '9');
    });

    it('should handle RUT foid without dash and call peopleService.getSearchCustomerByFoid', async () => {
      // Arrange
      const foid = '123456789';
      const foidType = 'RUT';
      const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
      const mockServiceResult = Result.Success(mockCustomerData);

      peopleService.getSearchCustomerByFoid.mockResolvedValue(mockServiceResult);

      // Act
      const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);
      expect(peopleService.getSearchCustomerByFoid).toHaveBeenCalledWith('12345678', '9');
    });

    it('should handle PASSPORT foid type without modification', async () => {
      // Arrange
      const foid = 'ABC123456';
      const foidType = 'PASSPORT';
      const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
      const mockServiceResult = Result.Success(mockCustomerData);

      peopleService.getSearchCustomerByFoid.mockResolvedValue(mockServiceResult);

      // Act
      const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);
      expect(peopleService.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, '');
    });

    it('should return a fail result when peopleService returns an error', async () => {
      // Arrange
      const foid = '12345678-9';
      const foidType = 'RUT';
      const mockError = new Error(errorsMock.serviceError.message);
      const mockServiceResult = Result.Fail(mockError);

      peopleService.getSearchCustomerByFoid.mockResolvedValue(mockServiceResult);

      // Act
      const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe(mockError);
      expect(peopleService.getSearchCustomerByFoid).toHaveBeenCalledWith('12345678', '9');
    });

    it('should handle phone number formatting correctly', async () => {
      // Arrange
      const foid = '12345678-9';
      const foidType = 'RUT';

      // Test different phone number formats
      const testCases = [
        {
          input: '56-9-12345678',
          expected: { phoneCountry: '56', phoneArea: '9', phoneNumber: '12345678' }
        },
        {
          input: null,
          expected: { phoneCountry: '', phoneArea: '', phoneNumber: '' }
        },
        {
          input: '56-9',
          expected: { phoneCountry: '56', phoneArea: '9', phoneNumber: '' }
        },
        {
          input: '56',
          expected: { phoneCountry: '56', phoneArea: '', phoneNumber: '' }
        }
      ];

      for (const testCase of testCases) {
        // Reset mocks for each test case
        jest.clearAllMocks();

        const mockCustomerData = require('../../__mocks__/people/getSearchCustomerByFoid.json').service;
        // Override the phone number for this test case
        mockCustomerData.fonoCelular = testCase.input;
        const mockServiceResult = Result.Success(mockCustomerData);

        peopleService.getSearchCustomerByFoid.mockResolvedValue(mockServiceResult);

        // Act
        const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

        // Assert
        expect(result.success).toBe(true);
        expect(result.data.phoneCountry).toBe(testCase.expected.phoneCountry);
        expect(result.data.phoneArea).toBe(testCase.expected.phoneArea);
        expect(result.data.phoneNumber).toBe(testCase.expected.phoneNumber);
      }
    });

    it('should handle null profile from service', async () => {
      // Arrange
      const foid = '12345678-9';
      const foidType = 'RUT';

      peopleService.getSearchCustomerByFoid.mockResolvedValue(Result.Success(null));

      // Act
      const result = await peopleFacade.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
    });
  });
});
