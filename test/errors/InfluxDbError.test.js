const InfluxDbError = require('../../src/errors/InfluxDbError');

describe('InfluxDbError', () => {
  it('should create an error with provided values', () => {
    // Arrange & Act
    const error = new InfluxDbError(400, true, 'Bad Request', 12345, 'Test Error');

    // Assert
    expect(error.statusCode).toBe(400);
    expect(error.alert).toBe(true);
    expect(error.message).toBe('Bad Request');
    expect(error.peopleid).toBe(12345);
    expect(error.otherdata).toBe('Test Error');
  });

  it('should create an error with default values when not provided', () => {
    // Arrange & Act
    const error = new InfluxDbError();

    // Assert
    expect(error.statusCode).toBe(500);
    expect(error.alert).toBe("");
    expect(error.message).toBe("");
    expect(error.peopleid).toBeNull();
    expect(error.otherdata).toBe("");
  });

  it('should create an error with mixed default and provided values', () => {
    // Arrange & Act
    const error = new InfluxDbError(404, false);

    // Assert
    expect(error.statusCode).toBe(404);
    expect(error.alert).toBe(false);
    expect(error.message).toBe("");
    expect(error.peopleid).toBeNull();
    expect(error.otherdata).toBe("");
  });
});
