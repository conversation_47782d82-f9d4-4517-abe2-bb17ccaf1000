const InternalServerError = require('../../src/errors/InternalServerError');

describe('InternalServerError', () => {
  it('should create an error with provided values', () => {
    // Arrange & Act
    const status = 500;
    const message = 'Internal Server Error';
    const name = 'CustomError';
    const errors = ['Error 1', 'Error 2'];
    const innerError = new Error('Inner error');
    
    const error = new InternalServerError(status, message, name, errors, innerError);

    // Assert
    expect(error.status).toBe(status);
    expect(error.message).toBe(message);
    expect(error.name).toBe(name);
    expect(error.errors).toEqual(errors);
  });

  it('should create an error with default values when not provided', () => {
    // Arrange & Act
    const status = 500;
    const message = 'Internal Server Error';
    
    const error = new InternalServerError(status, message);

    // Assert
    expect(error.status).toBe(status);
    expect(error.message).toBe(message);
    expect(error.name).toBe('Error');
    expect(error.errors).toEqual([]);
  });

  it('should create an error with mixed default and provided values', () => {
    // Arrange & Act
    const status = 500;
    const message = 'Internal Server Error';
    const name = 'CustomError';
    
    const error = new InternalServerError(status, message, name);

    // Assert
    expect(error.status).toBe(status);
    expect(error.message).toBe(message);
    expect(error.name).toBe(name);
    expect(error.errors).toEqual([]);
  });
});
