const healthController = require('../../src/controllers/health-controller');
const os = require('os');
const { InternalServerError } = require('../../src/errors');
const Result = require('../../src/utils/result');
const healthMock = require('../../__mocks__/health/health.json');
const errorsMock = require('../../__mocks__/errors/errors.json');

// Mock dependencies
jest.mock('os');

describe('Health Controller', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('health', () => {
    it('should return a success result with health data', async () => {
      // Arrange
      const mockHostname = healthMock.hostname;
      os.hostname.mockReturnValue(mockHostname);

      // Mock environment variables
      const originalEnv = process.env;
      process.env = {
        ...originalEnv,
        APP_BUILD: healthMock.app.build,
        APP_COMMIT: healthMock.app.commit,
        NODE_ENV: healthMock.app.environment
      };

      // Act
      const result = await healthController.health();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        hostname: healthMock.hostname,
        app: {
          version: expect.any(String), // From package.json
          build: healthMock.app.build,
          commit: healthMock.app.commit,
          name: expect.any(String), // From package.json
          environment: healthMock.app.environment,
        },
      });
      expect(os.hostname).toHaveBeenCalled();

      // Restore environment
      process.env = originalEnv;
    });

    it('should use default values when environment variables are not set', async () => {
      // Arrange
      const mockHostname = 'test-hostname';
      os.hostname.mockReturnValue(mockHostname);

      // Mock environment variables
      const originalEnv = process.env;
      const { APP_BUILD, APP_COMMIT, NODE_ENV, ...restEnv } = process.env;
      process.env = restEnv;

      // Act
      const result = await healthController.health();

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        hostname: mockHostname,
        app: {
          version: expect.any(String), // From package.json
          build: 'no-build',
          commit: 'no-commit',
          name: expect.any(String), // From package.json
          environment: 'local',
        },
      });
      expect(os.hostname).toHaveBeenCalled();

      // Restore environment
      process.env = originalEnv;
    });

    it('should return a fail result when an error occurs', async () => {
      // Arrange
      const mockError = new Error(errorsMock.testError.message);
      os.hostname.mockImplementation(() => {
        throw mockError;
      });

      // Act
      const result = await healthController.health();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InternalServerError);
      expect(result.error.message).toBe(errorsMock.testError.message);
      expect(os.hostname).toHaveBeenCalled();
    });

    it('should handle errors without constructor', async () => {
      // Arrange
      const mockError = Object.create(null); // Object without prototype
      mockError.message = errorsMock.errorWithoutConstructor.message;
      os.hostname.mockImplementation(() => {
        throw mockError;
      });

      // Act
      const result = await healthController.health();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InternalServerError);
      expect(result.error.message).toBe(errorsMock.errorWithoutConstructor.message);
      expect(result.error.name).toBe(errorsMock.errorWithoutConstructor.name); // Should use default 'Error' name
    });

    it('should handle errors without message', async () => {
      // Arrange
      const mockError = {};
      os.hostname.mockImplementation(() => {
        throw mockError;
      });

      // Act
      const result = await healthController.health();

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InternalServerError);
      expect(result.error.message).toBe(errorsMock.errorWithoutMessage.message);
    });
  });
});
