// Mock dependencies first
jest.mock('axios'); // Use the global axios mock from __mocks__/axios.js
jest.mock('@cocha/cocha-logger', () => require('../../__mocks__/logger/cocha-logger'));
jest.mock('../../src/utils/axios-factory', () => {
  // Import the global axios mock
  const mockAxios = require('axios');
  return {
    getInstance: jest.fn().mockReturnValue(mockAxios)
  };
});
jest.mock('../../src/service/configs-service');
jest.mock('../../src/service/people-service');
jest.mock('../../src/facade/facade');
jest.mock('../../src/utils/utils');

// Then import the modules
const peopleController = require('../../src/controllers/people-controller');
const peopleFacade = require('../../src/facade/facade');
const { ArgumentError } = require('common-errors');
const { InvalidFoidError } = require('../../src/errors');
const Result = require('../../src/utils/result');
const utils = require('../../src/utils/utils');
const mockAxios = require('axios'); // Import the global axios mock for use in tests

describe('People Controller', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('getByFoid', () => {
    it('should return a success result with data when valid parameters are provided', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockData = require('../../__mocks__/people/getByFoid.json').service;

      peopleFacade.getPeopleByFoid.mockResolvedValue(Result.Success(mockData));

      // Act
      const result = await peopleController.getByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
      expect(peopleFacade.getPeopleByFoid).toHaveBeenCalledWith(foid, foidType);
    });

    it('should return a fail result when foidType is not provided', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = null;

      // Act
      const result = await peopleController.getByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(ArgumentError);
      expect(peopleFacade.getPeopleByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foid is not provided', async () => {
      // Arrange
      const foid = null;
      const foidType = 'RUT';

      // Act
      const result = await peopleController.getByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(ArgumentError);
      expect(peopleFacade.getPeopleByFoid).not.toHaveBeenCalled();
    });
  });

  describe('getSearchCustomerByFoid', () => {
    beforeEach(() => {
      // Mock utils functions
      utils.isValidFoidType.mockReturnValue(true);
      utils.isValidFoid.mockReturnValue(true);
    });

    it('should return a success result with people data when valid parameters are provided', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockData = require('../../__mocks__/people/getSearchCustomerByFoid.json').controller.people;

      peopleFacade.getSearchCustomerByFoid.mockResolvedValue(Result.Success(mockData));

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(true);
      expect(result.data).toEqual({ people: mockData });
      expect(peopleFacade.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
    });

    it('should return a fail result when foid is not provided', async () => {
      // Arrange
      const foid = null;
      const foidType = 'RUT';

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foid is :foid', async () => {
      // Arrange
      const foid = ':foid';
      const foidType = 'RUT';

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foid is not a number and foidType is RUT', async () => {
      // Arrange
      const foid = 'abc123';
      const foidType = 'RUT';

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foidType is not provided', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = null;

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foidType is :foidType', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = ':foidType';

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foidType is invalid', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'INVALID';
      utils.isValidFoidType.mockReturnValue(false);

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when foid exceeds maximum length', async () => {
      // Arrange
      const foid = '12345678901234567890';
      const foidType = 'RUT';

      // Make sure isValidFoidType returns true but isValidFoid returns false
      utils.isValidFoidType.mockReturnValue(true);
      utils.isValidFoid.mockReturnValue(false);

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBeInstanceOf(InvalidFoidError);
      expect(peopleFacade.getSearchCustomerByFoid).not.toHaveBeenCalled();
    });

    it('should return a fail result when facade returns an error', async () => {
      // Arrange
      const foid = '12345678';
      const foidType = 'RUT';
      const mockError = new Error('Service error');
      peopleFacade.getSearchCustomerByFoid.mockResolvedValue(Result.Fail(mockError));

      // Act
      const result = await peopleController.getSearchCustomerByFoid(foid, foidType);

      // Assert
      expect(result.success).toBe(false);
      expect(result.error).toBe(mockError);
      expect(peopleFacade.getSearchCustomerByFoid).toHaveBeenCalledWith(foid, foidType);
    });
  });
});