// Mock dependencies first
jest.mock('express', () => {
  const mockApp = {
    disable: jest.fn(),
    use: jest.fn(),
    listen: jest.fn((options, callback) => {
      callback();
      return mockServer;
    })
  };
  const mockServer = {
    address: jest.fn(() => ({ port: 8004 }))
  };
  const mockExpress = jest.fn(() => mockApp);
  mockExpress.Router = jest.fn(() => ({}));
  return mockExpress;
});

jest.mock('body-parser', () => ({
  json: jest.fn(() => 'json-middleware'),
  urlencoded: jest.fn(() => 'urlencoded-middleware')
}));

jest.mock('cors', () => jest.fn(() => 'cors-middleware'));
jest.mock('dotenv', () => ({ config: jest.fn() }));
jest.mock('swagger-ui-express', () => ({
  serve: 'swagger-ui-serve',
  setup: jest.fn(() => 'swagger-ui-setup')
}));
jest.mock('../docs/swagger', () => ({ swagger: 'mock-swagger-spec' }));
jest.mock('../src/config/routes', () => 'mock-router');
jest.mock('../src/utils/logger-factory', () => {
  const mockLogger = {
    info: jest.fn()
  };
  return {
    getLogger: jest.fn(() => mockLogger)
  };
});

describe('Index', () => {
  let express;
  let bodyParser;
  let cors;
  let dotenv;
  let logger;
  let originalEnv;

  beforeEach(() => {
    // Save original process.env
    originalEnv = { ...process.env };

    // Clear all mocks before each test
    jest.clearAllMocks();

    // Reset modules
    jest.resetModules();

    // Import dependencies
    express = require('express');
    bodyParser = require('body-parser');
    cors = require('cors');
    dotenv = require('dotenv');
    logger = require('../src/utils/logger-factory').getLogger();
  });

  afterEach(() => {
    // Restore original process.env
    process.env = originalEnv;
  });

  it('should set up the server with middleware and routes', () => {
    // Arrange
    process.env.NODE_ENV = 'test';
    const port = 8004;

    // Act
    require('../src/index');

    // Assert
    expect(express).toHaveBeenCalledWith({});
    expect(express().disable).toHaveBeenCalledWith('x-powered-by');
    expect(cors).toHaveBeenCalledWith({
      origin: [/cocha\.com/],
      methods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedHeaders: ['TrackId', 'FlowId', 'Origin', 'X-Requested-With', 'Content-Type', 'Accept', 'Authorization', 'PaymentIntention']
    });
    expect(express().use).toHaveBeenCalledWith('cors-middleware');
    expect(bodyParser.json).toHaveBeenCalled();
    expect(express().use).toHaveBeenCalledWith('json-middleware');
    expect(bodyParser.urlencoded).toHaveBeenCalledWith({ extended: true });
    expect(express().use).toHaveBeenCalledWith('urlencoded-middleware');
    expect(express().use).toHaveBeenCalledWith('/', 'mock-router');
    expect(express().listen).toHaveBeenCalledWith({ port: 8004 }, expect.any(Function));
    expect(logger.info).toHaveBeenCalledWith(`Server started as 'test' environment on http://localhost:${port}`);
  });

  it('should set up CORS headers middleware', () => {
    // Arrange
    process.env.NODE_ENV = 'test';

    // Act
    require('../src/index');

    // Assert
    // Find the middleware function that was passed to express().use
    const calls = express().use.mock.calls;
    const middlewareCall = calls.find(call => typeof call[0] === 'function');
    expect(middlewareCall).toBeDefined();

    // Create mock request, response, and next objects
    const req = {};
    const res = { set: jest.fn() };
    const next = jest.fn();

    // Call the middleware function
    middlewareCall[0](req, res, next);

    // Verify the middleware behavior
    expect(res.set).toHaveBeenCalledWith(
      'Access-Control-Allow-Headers',
      'TrackId, FlowId, Origin, X-Requested-With, Content-Type, Accept, Authorization, PaymentIntention'
    );
    expect(res.set).toHaveBeenCalledWith('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE');
    expect(next).toHaveBeenCalled();
  });

  it('should set up swagger UI when not in production', () => {
    // Arrange
    process.env.NODE_ENV = 'development';

    // Act
    require('../src/index');

    // Assert
    // Check that swagger routes are set up
    expect(express().use).toHaveBeenCalledWith('/api-docs', expect.any(Function));
    expect(express().use).toHaveBeenCalledWith('/docs/', 'swagger-ui-serve', 'swagger-ui-setup');

    // Test the API docs endpoint handler
    const calls = express().use.mock.calls;
    const apiDocsCall = calls.find(call => call[0] === '/api-docs');
    expect(apiDocsCall).toBeDefined();

    // Create mock request, response objects
    const req = {};
    const res = { send: jest.fn() };

    // Call the handler function
    apiDocsCall[1](req, res);

    // Verify the handler behavior
    expect(res.send).toHaveBeenCalledWith({ swagger: 'mock-swagger-spec' });
  });

  it('should not set up swagger UI in production', () => {
    // Arrange
    process.env.NODE_ENV = 'production';

    // Act
    require('../src/index');

    // Assert
    // Check that swagger routes are not set up
    expect(express().use).not.toHaveBeenCalledWith('/api-docs', expect.any(Function));
    expect(express().use).not.toHaveBeenCalledWith('/docs/', 'swagger-ui-serve', 'swagger-ui-setup');
  });

  it('should load environment variables from .env when not in production', () => {
    // Arrange
    process.env.NODE_ENV = 'development';

    // Act
    require('../src/index');

    // Assert
    expect(dotenv.config).toHaveBeenCalled();
  });

  it('should use default port when PORT environment variable is not set', () => {
    // Arrange
    delete process.env.PORT;

    // Act
    require('../src/index');

    // Assert
    expect(express().listen).toHaveBeenCalledWith({ port: 8004 }, expect.any(Function));
  });

  it('should use PORT environment variable when set', () => {
    // Arrange
    process.env.PORT = '9000';

    // Act
    require('../src/index');

    // Assert
    expect(express().listen).toHaveBeenCalledWith({ port: '9000' }, expect.any(Function));
  });

  it('should use default environment when NODE_ENV is not set', () => {
    // Arrange
    delete process.env.NODE_ENV;

    // Act
    require('../src/index');

    // Assert
    expect(logger.info).toHaveBeenCalledWith('Server started as \'local\' environment on http://localhost:8004');
  });
});
