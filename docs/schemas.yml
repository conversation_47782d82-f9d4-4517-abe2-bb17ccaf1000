components:
   schemas:
      GetPeopleResponse:
        type: object
        properties:
          people:
            $ref: '#/components/schemas/People'
      GetClientResponse:
        type: object
        properties:
          client:
            $ref: '#/components/schemas/Client'
      InternalServerError:
         type: object
         properties:
          message:
             type: string
             example: An unknown error happened
             description: Description of the error
          name:
             type: string
             example: Error
             description: Name of the error
          errors:
             type: object
             additionalProperties: false
             description: Arguments passed to the error
      ApiSmartError:
          type: object
          properties:
            message:
              type: string
              example: Request failed with status code 400
              description: Description of the error
            name:
              type: string
              example: ApiSmartError
              description: Name of the error
            errors:
              type: object
              additionalProperties: false
              description: Arguments passed to the error
      Health:
        type: object
        properties:
          hostname:
            type: string
            description: Nombre del host o pod donde se ejecuta.
            example: api-manager-people-668bc9bc5f-42z5r
          app:
            $ref: '#/components/schemas/App'
      App:
        type: object
        properties:
          version:
            type: string
            description: The app version.
            example: 1.1.4
          build:
            type: string
            description: Build of project.
            example: 78
          commit:
            type: string
            description: Number of commit.
            example: 0d8d3182e4552941f7394ab978b2f6b6dcb5cdce
          name:
            type: string
            description: Name of the Api.
            example: api-manager-connection-cart
          environment:
            type: string
            description: execution environment.
            example: development
      People:
        type: object
        properties:
          id:
            type: integer
            example: 12
          foid:
            type: string
            example: "13.714.931-1"
          foidType:
            type: string
            example: "RUT"
          foidCountry:
            type: string
            example: "CL"
          name:
            type: string
            example: "rodrigo"
          firstLastName:
            type: string
            example: "riffo"
          secondLastName:
            type: string
            example: "guerra"
          gender:
            type: string
          email:
            type: string
          foidExpiration:
            type: string
            format: date-time
            nullable: true
          foidIssued:
            type: string
            format: date-time
            nullable: true
          birthDate:
            type: string
            format: date-time
            nullable: true
          age:
            type: integer
            nullable: true
          type:
            type: string
            example: "ADT"
            
      Client:
        type: object
        properties:
          id:
            type: integer
            example: 12
          foid:
            type: string
            example: "13.714.931-1"
          foidType:
            type: string
            example: "RUT"
          foidCountry:
            type: string
            example: "CL"
          name:
            type: string
            example: "rodrigo"
          firstLastName:
            type: string
            example: "riffo"
          secondLastName:
            type: string
            example: "guerra"
          gender:
            type: string
          email:
            type: string
          foidExpiration:
            type: string
            format: date-time
            nullable: true
          foidIssued:
            type: string
            format: date-time
            nullable: true
          birthDate:
            type: string
            format: date-time
            nullable: true
          age:
            type: integer
            nullable: true
          type:
            type: string
            example: "ADT"
          phoneCountry:
            type: string
            example: "+56"
          phoneArea:
            type: string
            example: "9"
          phoneNumber:
            type: string
            example: "56336098"