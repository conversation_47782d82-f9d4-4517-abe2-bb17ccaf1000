tags:
  - name: Health
    description: Health Endpoint.
    externalDocs:
      description: More details
      url: https://bitbucket.org/cocha-digital/api-manager-people/src/master/README.md
/health:
  get:
    tags: [Health]
    summary: This endpoint provides information about the health of the service.
    description: This endpoint provides information about the health and status of the current service.
    responses:
      200:
        description: OK.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Health'
      500:
        description: Error.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalServerError'