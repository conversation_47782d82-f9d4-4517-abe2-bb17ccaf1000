const swaggerJSDoc = require('swagger-jsdoc');
const fs = require('fs');


let description = 'El API Manager People es la API que se encarga de gestionar información de pasajeros y clientes. Obtiene los datos a través de la API Manager Connection People y Smart, centralizando el acceso a la información de personas en el ecosistema.';
// Leer el contenido del README.md
try {
  description = fs.readFileSync('./README.md', 'utf8');
} catch (e) {
  // eslint-disable-next-line no-console
  console.error('Error:', e.stack);
}


const swaggerOptions = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'API MANAGER PEOPLE',
      version: '2.0.0',
      description: description,
    },
    servers: [
      {
        url: 'http://localhost:8004',
        description: 'localhost',
      },
      {
        url: 'https://apis-qa-int.cocha.cloud/people-mngr',
        description: 'qa',
      },
      {
        url: 'https://apis-dev-int.cocha.cloud/people-mngr',
        description: 'dev',
      },
    ],
  },
  apis: ['./docs/*.yml'],
};

const swaggerSpec = swaggerJSDoc(swaggerOptions);

module.exports = swaggerSpec;
