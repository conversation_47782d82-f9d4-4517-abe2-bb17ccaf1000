tags:
  - name: People
    description: People Endpoint.
    externalDocs:
      description: More details
      url: https://bitbucket.org/cocha-digital/api-manager-people/src/master/README.md


/people/{foid}/{foidType}:
  get:
    tags:
      - People
    parameters:
      - name: foid
        in: path
        required: true
        description: People foid.
        schema:
          type: string
      - name: foidType
        in: path
        required: true
        description: foidType (RUT, PASSPORT).
        schema:
          type: string
    responses:
      200:
        description: OK.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetPeopleResponse'
      400:
        description: Api Blsmart Error.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiSmartError'
      500:
        description: Internal Server Error Or Invalid Foid Error.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalServerError'