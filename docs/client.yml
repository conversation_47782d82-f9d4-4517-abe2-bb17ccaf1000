tags:
  - name: Client
    description: Client Endpoint.
    externalDocs:
      description: More details
      url: https://bitbucket.org/cocha-digital/api-manager-people/src/master/README.md

/{foid}/{foidType}:
  get:
    tags:
      - Client
    parameters:
      - name: foid
        in: path
        required: true
        description: People Foid.
        schema:
          type: string
      - name: foidType
        in: path
        required: true
        description: People Foid Type (RUT, PASSPORT).
        schema:
          type: string
    responses:
      200:
        description: OK.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/GetClientResponse'
      400:
        description: Api Blsmart Error.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApiSmartError'
      500:
        description: Internal Server Error.
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/InternalServerError'
          
