FROM node:20.11.1-slim

COPY . /node-app
WORKDIR /node-app

RUN npm ci --only=prod

ARG build=no-build
ARG version=no-hash
ARG repo=no-repo
ARG tag=no-tag
ARG app_name=api-manager-people
ARG app_type=api
ARG team_name=checkout

ENV APP_BUILD=${build} \
  APP_COMMIT=${version} \
  APP_REPO=${repo} \
  APP_TAG=${tag} \
  APP_NAME=${app_name} \
  APP_TYPE=${app_type} \
  TEAM_NAME=${team_name}

EXPOSE ${PORT}

CMD ["node", "src/index.js"]