version: 3
image:
  name: gcr.io/cocha-backups/image-deploy-gcp
  username: _json_key
  password: '$GCP_BACKUPS_BITBUCKET_SA_CLEAR'
options:
  docker: true
definitions:
  steps:
    - step: &docker
        name: Docker Publish
        script:
          - docker-publish-v3
    - step: &deploy-cr
        name: Docker Build and Deploy
        deployment: development
        script:
          - k8s-publish-v4
    - step: &sonarcloud
        name: Sonar Cloud Inspection
        script:
          - npm-sonarcloud-jdk17-v2 development
    - step: &sonarcloud-prod
        name: Sonar Cloud Inspection (Production)
        script:
          - npm-sonarcloud-jdk17-v2 production      
pipelines:
  custom:
    microambiente:
    - variables:
        - name: ConfigMapFiles
        - name: ConfigMaps
    - step: 
        <<: *docker
        name: Docker Publish
        script:
          - docker-publish-v3 menv
    - step:
        name: Docker Build and Deploy
        deployment: dev-menv
        script:
          - k8s-publish-menv
    code-inspection:
    - step:
        <<: *sonarcloud-prod
    deploy-api-k8s:
    - step:
        <<: *sonarcloud
        name: Sonar Cloud Inspection
    - step:
        <<: *docker
        name: Publish Container
    - step:
        <<: *deploy-cr
        name: Deploy to Develop
        deployment: development
    - step:
        <<: *sonarcloud-prod    
    - step:
        <<: *deploy-cr
        name: Deploy to Quality
        trigger: manual
        deployment: qa
  tags:
    v*.*.*:
    - step:
        <<: *sonarcloud-prod
    - step: 
        <<: *docker
        name: Docker Publish
        script:
          - docker-publish-v3 production
    - step:
        <<: *deploy-cr
        name: Deploy to Production
        trigger: manual
        deployment: production