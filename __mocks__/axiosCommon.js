/**
 * Mock for axiosCommon
 */

// Create mock axios instance with all HTTP methods
const mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn()
};

// Create axiosWithLogger mock that returns the mockAxios instance
const axiosWithLogger = {
  getInstance: jest.fn().mockReturnValue(mockAxios)
};

module.exports = {
  getInstance: () => axiosWithLogger.getInstance(),
  axiosWithLogger
};