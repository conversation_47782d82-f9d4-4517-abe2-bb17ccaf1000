/**
 * Mock for @cocha/cocha-logger
 */

const mockLogger = {
  info: jest.fn(),
  error: jest.fn(),
  warn: jest.fn(),
  debug: jest.fn(),
  sendGrafanaLogObj: jest.fn()
};

const mockAxios = {
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn(),
  patch: jest.fn()
};

const axiosWithLogger = {
  getInstance: jest.fn().mockReturnValue(mockAxios)
};

module.exports = {
  getLogger: jest.fn().mockReturnValue(mockLogger),
  axiosWithLogger
};
